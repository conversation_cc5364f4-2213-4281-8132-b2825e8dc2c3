import { ChevronDown } from 'lucide-react';
import { useState } from 'react';

import { TonPriceDisplay } from '@/components/shared/ton-price-display';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import type { OrderEntity } from '@/constants/core.constants';
import { OrderStatus } from '@/constants/core.constants';
import { cn } from '@/lib/utils';

interface UserOrderPaymentDetailsSectionProps {
  order: OrderEntity;
}

interface FeeRowProps {
  label: string;
  amount: number;
  description?: string;
  isLast?: boolean;
}

function FeeRow({ label, amount, description, isLast = false }: FeeRowProps) {
  return (
    <div
      className={`flex justify-between items-start py-2 ${
        !isLast ? 'border-b border-[#3a4a5c]/20' : ''
      }`}
    >
      <div className="flex-1">
        <span className="text-[#f5f5f5] text-sm font-medium">{label}</span>
        {description && (
          <p className="text-xs text-[#708499] mt-1">{description}</p>
        )}
      </div>
      <TonPriceDisplay
        amount={amount}
        size={16}
        className="text-sm font-semibold text-[#6ab2f2]"
        showUnit
      />
    </div>
  );
}

export function UserOrderPaymentDetailsSection({
  order,
}: UserOrderPaymentDetailsSectionProps) {
  const [isOpen, setIsOpen] = useState(false);

  if (!order.fees) {
    return null;
  }

  const { fees } = order;
  const hasSecondaryPrice =
    order.secondaryMarketPrice && order.secondaryMarketPrice > 0;
  const showPurchaseFee =
    order.status === OrderStatus.PAID ||
    order.status === OrderStatus.GIFT_SENT_TO_RELAYER ||
    order.status === OrderStatus.FULFILLED;

  const sellerLockedAmount =
    (order.price * fees.seller_locked_percentage) / 10000;
  const buyerLockedAmount =
    (order.price * fees.buyer_locked_percentage) / 10000;
  const purchaseFeeAmount = showPurchaseFee
    ? (order.price * fees.purchase_fee) / 10000
    : 0;
  const resellPurchaseFeeAmount = hasSecondaryPrice
    ? ((order.secondaryMarketPrice || 0) * fees.resell_purchase_fee) / 10000
    : 0;
  const resellerEarningsAmount = order.reseller_earnings_for_seller || 0;

  const feeItems = [
    {
      label: 'Seller Locked Collateral',
      amount: sellerLockedAmount,
      description: `${(fees.seller_locked_percentage / 100).toFixed(1)}% of order price`,
    },
    {
      label: 'Buyer Locked Collateral',
      amount: buyerLockedAmount,
      description: `${(fees.buyer_locked_percentage / 100).toFixed(1)}% of order price`,
    },
  ];

  if (showPurchaseFee && purchaseFeeAmount > 0) {
    feeItems.push({
      label: 'Purchase Fee',
      amount: purchaseFeeAmount,
      description: `${(fees.purchase_fee / 100).toFixed(1)}% marketplace fee`,
    });
  }

  if (hasSecondaryPrice && resellPurchaseFeeAmount > 0) {
    feeItems.push({
      label: 'Resell Purchase Fee',
      amount: resellPurchaseFeeAmount,
      description: `${(fees.resell_purchase_fee / 100).toFixed(1)}% fee on secondary market price`,
    });
  }

  if (hasSecondaryPrice && resellerEarningsAmount > 0) {
    feeItems.push({
      label: 'Reseller Earnings for Seller',
      amount: resellerEarningsAmount,
      description: 'Accumulated earnings from resales',
    });
  }

  return (
    <div className="border border-[#3a4a5c]/30 rounded-lg overflow-hidden">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger className="w-full p-4 flex items-center justify-between hover:bg-[#232e3c]/50 transition-colors">
          <div className="flex items-center gap-2">
            <span className="text-[#f5f5f5] font-medium">
              Order Details & Fees
            </span>
          </div>
          <ChevronDown
            className={cn(
              'w-4 h-4 text-[#708499] transition-transform duration-200',
              isOpen && 'rotate-180',
            )}
          />
        </CollapsibleTrigger>

        <CollapsibleContent>
          <div className="px-4 pb-4 space-y-1">
            {feeItems.map((item, index) => (
              <FeeRow
                key={item.label}
                label={item.label}
                amount={item.amount}
                description={item.description}
                isLast={index === feeItems.length - 1}
              />
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
